"""
Cyber Threat Intelligence (CTI) Agent for analyzing cybersecurity threats, APTs, and IOCs.
"""

import re
import dspy
from typing import Dict, List, Any, Optional, Tuple
from datetime import datetime, timedelta
from langchain.tools import BaseTool

from .base_agent import BaseOSINTAgent
from tools.crawl4ai_wrapper import Crawl4AITool


class ThreatAnalysisSignature(dspy.Signature):
    """DSPy signature for cyber threat analysis tasks."""
    
    query = dspy.InputField(desc="Cyber threat analysis query")
    context = dspy.InputField(desc="Threat landscape context and background")
    indicators = dspy.InputField(desc="Indicators of Compromise (IOCs) and artifacts")
    
    threat_assessment = dspy.OutputField(desc="Comprehensive threat assessment")
    attribution = dspy.OutputField(desc="Threat actor attribution and confidence level")
    iocs = dspy.OutputField(desc="Extracted and validated IOCs")
    recommendations = dspy.OutputField(desc="Security recommendations and countermeasures")


class IOCExtractionSignature(dspy.Signature):
    """DSPy signature for IOC extraction from text."""
    
    text = dspy.InputField(desc="Text content to analyze for IOCs")
    
    ip_addresses = dspy.OutputField(desc="Extracted IP addresses")
    domains = dspy.OutputField(desc="Extracted domain names")
    file_hashes = dspy.OutputField(desc="Extracted file hashes (MD5, SHA1, SHA256)")
    urls = dspy.OutputField(desc="Extracted malicious URLs")
    email_addresses = dspy.OutputField(desc="Extracted email addresses")


class CTIAgent(BaseOSINTAgent):
    """
    Specialized agent for Cyber Threat Intelligence analysis.
    
    Focuses on:
    - Threat actor tracking and attribution
    - IOC extraction and validation
    - Malware analysis and family identification
    - Campaign tracking and correlation
    - Vulnerability intelligence
    - Dark web monitoring
    """
    
    def __init__(self, **kwargs):
        """Initialize the CTI Agent."""
        
        # Set default values for CTI agent
        defaults = {
            "name": "CTIAnalyst",
            "role": "Senior Cyber Threat Intelligence Analyst",
            "goal": """Analyze cyber threats, track threat actors, extract and validate indicators 
                      of compromise, and provide actionable intelligence to enhance cybersecurity posture.""",
            "backstory": """You are an expert cyber threat intelligence analyst with extensive experience 
                           in malware analysis, threat hunting, and adversary tracking. You have worked 
                           with government agencies and private sector organizations to identify, analyze, 
                           and mitigate advanced persistent threats. You excel at correlating disparate 
                           indicators to build comprehensive threat profiles."""
        }
        
        # Merge with provided kwargs
        for key, value in defaults.items():
            kwargs.setdefault(key, value)
        
        super().__init__(**kwargs)
        
        # Initialize DSPy modules
        self.threat_analysis_module = dspy.ChainOfThought(ThreatAnalysisSignature)
        self.ioc_extraction_module = dspy.ChainOfThought(IOCExtractionSignature)
        
        # IOC regex patterns
        self.ioc_patterns = {
            'ip': re.compile(r'\b(?:[0-9]{1,3}\.){3}[0-9]{1,3}\b'),
            'domain': re.compile(r'\b[a-zA-Z0-9]([a-zA-Z0-9\-]{0,61}[a-zA-Z0-9])?(\.[a-zA-Z0-9]([a-zA-Z0-9\-]{0,61}[a-zA-Z0-9])?)*\.[a-zA-Z]{2,}\b'),
            'md5': re.compile(r'\b[a-fA-F0-9]{32}\b'),
            'sha1': re.compile(r'\b[a-fA-F0-9]{40}\b'),
            'sha256': re.compile(r'\b[a-fA-F0-9]{64}\b'),
            'email': re.compile(r'\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,}\b'),
            'url': re.compile(r'https?://[^\s<>"{}|\\^`\[\]]+')
        }
        
        # Known threat actor groups
        self.threat_actors = [
            "APT1", "APT28", "APT29", "APT40", "Lazarus", "DarkHydrus", 
            "Carbanak", "FIN7", "Equation Group", "Turla", "Fancy Bear",
            "Cozy Bear", "Sandworm", "Dragonfly", "Kimsuky"
        ]
    
    def _get_specialized_tools(self) -> List[BaseTool]:
        """Get tools specific to CTI analysis."""
        tools = []
        
        # Add web crawling tool for security blogs and threat reports
        tools.append(Crawl4AITool(
            focus_keywords=[
                "malware", "APT", "threat actor", "IOC", "indicators",
                "cybersecurity", "vulnerability", "exploit", "campaign"
            ]
        ))
        
        return tools
    
    def analyze(self, query: str, threat_type: str = "general", **kwargs) -> Dict[str, Any]:
        """
        Perform cyber threat intelligence analysis.
        
        Args:
            query: The threat or security question to analyze
            threat_type: Type of threat (malware, apt, vulnerability, etc.)
            **kwargs: Additional parameters
            
        Returns:
            Comprehensive threat intelligence analysis
        """
        self.logger.info(f"Starting CTI analysis: {query}")
        
        # Gather threat context
        context = self._gather_threat_context(query, threat_type)
        indicators = self._collect_indicators(query)
        
        # Perform DSPy-powered analysis
        analysis_result = self.threat_analysis_module(
            query=query,
            context=context,
            indicators=indicators
        )
        
        # Extract IOCs from the analysis
        extracted_iocs = self._extract_iocs_from_text(query + " " + str(analysis_result))
        
        # Structure the results
        result = {
            "query": query,
            "threat_type": threat_type,
            "threat_assessment": analysis_result.threat_assessment,
            "attribution": analysis_result.attribution,
            "iocs": extracted_iocs,
            "recommendations": analysis_result.recommendations,
            "context": context,
            "timestamp": datetime.now().isoformat(),
            "agent": self.name,
            "confidence_score": self._calculate_confidence(analysis_result),
            "related_campaigns": self._identify_related_campaigns(query),
            "mitigation_strategies": self._generate_mitigations(analysis_result)
        }
        
        return result
    
    def extract_iocs(self, text: str) -> Dict[str, List[str]]:
        """
        Extract Indicators of Compromise from text using both regex and DSPy.
        
        Args:
            text: Text to analyze for IOCs
            
        Returns:
            Dictionary of extracted IOCs by type
        """
        # Use DSPy for intelligent extraction
        dspy_result = self.ioc_extraction_module(text=text)
        
        # Use regex for pattern matching
        regex_iocs = self._extract_iocs_from_text(text)
        
        # Combine and deduplicate results
        combined_iocs = {
            "ip_addresses": list(set(regex_iocs.get("ip_addresses", []))),
            "domains": list(set(regex_iocs.get("domains", []))),
            "file_hashes": list(set(regex_iocs.get("file_hashes", []))),
            "urls": list(set(regex_iocs.get("urls", []))),
            "email_addresses": list(set(regex_iocs.get("email_addresses", [])))
        }
        
        return combined_iocs
    
    def _extract_iocs_from_text(self, text: str) -> Dict[str, List[str]]:
        """Extract IOCs using regex patterns."""
        iocs = {
            "ip_addresses": self.ioc_patterns['ip'].findall(text),
            "domains": [],
            "file_hashes": [],
            "urls": self.ioc_patterns['url'].findall(text),
            "email_addresses": self.ioc_patterns['email'].findall(text)
        }
        
        # Extract domains (filter out IPs)
        domain_matches = self.ioc_patterns['domain'].findall(text)
        for match in domain_matches:
            domain = match[0] if isinstance(match, tuple) else match
            if not self.ioc_patterns['ip'].match(domain):
                iocs["domains"].append(domain)
        
        # Extract file hashes
        iocs["file_hashes"].extend(self.ioc_patterns['md5'].findall(text))
        iocs["file_hashes"].extend(self.ioc_patterns['sha1'].findall(text))
        iocs["file_hashes"].extend(self.ioc_patterns['sha256'].findall(text))
        
        # Remove duplicates
        for key in iocs:
            iocs[key] = list(set(iocs[key]))
        
        return iocs
    
    def _gather_threat_context(self, query: str, threat_type: str) -> str:
        """Gather relevant threat context."""
        context_parts = [
            f"Analyzing {threat_type} threat type.",
            "Current threat landscape includes sophisticated APT groups, ransomware operations, and supply chain attacks.",
            "Consider MITRE ATT&CK framework tactics, techniques, and procedures."
        ]
        
        # Add threat-specific context
        if threat_type == "apt":
            context_parts.append("Focus on advanced persistent threat characteristics, attribution, and long-term campaigns.")
        elif threat_type == "malware":
            context_parts.append("Analyze malware families, variants, and behavioral patterns.")
        elif threat_type == "vulnerability":
            context_parts.append("Assess vulnerability exploitation patterns and associated threat actors.")
        
        return " ".join(context_parts)
    
    def _collect_indicators(self, query: str) -> str:
        """Collect relevant indicators for analysis."""
        # This would typically involve calling search tools and threat feeds
        return f"Indicators collected for threat analysis of: {query}"
    
    def _calculate_confidence(self, analysis_result) -> float:
        """Calculate confidence score for the analysis."""
        # Simple confidence calculation based on attribution confidence
        attribution = analysis_result.attribution.lower()
        if "high confidence" in attribution:
            return 0.9
        elif "medium confidence" in attribution:
            return 0.7
        elif "low confidence" in attribution:
            return 0.4
        else:
            return 0.5
    
    def _identify_related_campaigns(self, query: str) -> List[str]:
        """Identify related threat campaigns."""
        query_lower = query.lower()
        related = []
        
        # Check for known threat actor mentions
        for actor in self.threat_actors:
            if actor.lower() in query_lower:
                related.append(f"Campaigns attributed to {actor}")
        
        return related
    
    def _generate_mitigations(self, analysis_result) -> List[str]:
        """Generate mitigation strategies based on analysis."""
        mitigations = [
            "Implement network segmentation and monitoring",
            "Deploy endpoint detection and response (EDR) solutions",
            "Maintain updated threat intelligence feeds",
            "Conduct regular security awareness training",
            "Implement zero-trust architecture principles"
        ]
        
        return mitigations
    
    def track_threat_actor(self, actor_name: str) -> Dict[str, Any]:
        """
        Track a specific threat actor and their activities.
        
        Args:
            actor_name: Name of the threat actor to track
            
        Returns:
            Threat actor profile and recent activities
        """
        query = f"Recent activities and TTPs of threat actor {actor_name}"
        analysis = self.analyze(query, threat_type="apt")
        
        profile = {
            "actor_name": actor_name,
            "analysis": analysis,
            "tracking_started": datetime.now().isoformat(),
            "last_updated": datetime.now().isoformat(),
            "known_aliases": self._get_actor_aliases(actor_name),
            "primary_targets": self._get_actor_targets(actor_name),
            "preferred_ttps": self._get_actor_ttps(actor_name)
        }
        
        return profile
    
    def _get_actor_aliases(self, actor_name: str) -> List[str]:
        """Get known aliases for a threat actor."""
        # This would typically query a threat intelligence database
        alias_map = {
            "APT28": ["Fancy Bear", "Pawn Storm", "Sofacy", "Sednit"],
            "APT29": ["Cozy Bear", "The Dukes", "CozyDuke"],
            "Lazarus": ["Hidden Cobra", "Guardians of Peace"],
            "DarkHydrus": ["DarkHydrus Group"]
        }
        return alias_map.get(actor_name, [])
    
    def _get_actor_targets(self, actor_name: str) -> List[str]:
        """Get typical targets for a threat actor."""
        # This would typically query a threat intelligence database
        target_map = {
            "APT28": ["Government", "Military", "Defense contractors"],
            "APT29": ["Government", "Think tanks", "Healthcare"],
            "Lazarus": ["Financial institutions", "Cryptocurrency exchanges"],
            "DarkHydrus": ["Government", "Education", "Critical infrastructure"]
        }
        return target_map.get(actor_name, ["Unknown"])
    
    def _get_actor_ttps(self, actor_name: str) -> List[str]:
        """Get known TTPs for a threat actor."""
        # This would typically query a threat intelligence database
        ttp_map = {
            "APT28": ["Spear phishing", "Zero-day exploits", "Living off the land"],
            "APT29": ["Supply chain attacks", "Cloud exploitation", "Steganography"],
            "Lazarus": ["Watering hole attacks", "Custom malware", "Financial theft"],
            "DarkHydrus": ["PowerShell scripts", "Credential harvesting", "Lateral movement"]
        }
        return ttp_map.get(actor_name, ["Unknown"])
    
    def generate_ioc_report(self, iocs: Dict[str, List[str]], context: str = "") -> Dict[str, Any]:
        """
        Generate a structured IOC report.
        
        Args:
            iocs: Dictionary of IOCs by type
            context: Additional context about the IOCs
            
        Returns:
            Structured IOC report
        """
        total_iocs = sum(len(ioc_list) for ioc_list in iocs.values())
        
        report = {
            "title": "Indicators of Compromise Report",
            "date": datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
            "analyst": self.name,
            "context": context,
            "summary": {
                "total_indicators": total_iocs,
                "ip_addresses": len(iocs.get("ip_addresses", [])),
                "domains": len(iocs.get("domains", [])),
                "file_hashes": len(iocs.get("file_hashes", [])),
                "urls": len(iocs.get("urls", [])),
                "email_addresses": len(iocs.get("email_addresses", []))
            },
            "indicators": iocs,
            "recommendations": [
                "Block identified IP addresses at network perimeter",
                "Add domains to DNS blacklist",
                "Search for file hashes in endpoint systems",
                "Monitor for URL patterns in web traffic",
                "Investigate email addresses for phishing campaigns"
            ],
            "confidence": "Medium",
            "valid_until": (datetime.now() + timedelta(days=30)).strftime("%Y-%m-%d")
        }
        
        return report
