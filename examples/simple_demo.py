#!/usr/bin/env python3
"""
Simple demonstration of the OSINT framework capabilities.
"""

import os
import sys
import json
from datetime import datetime

# Add project root to path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from dotenv import load_dotenv

# Load environment variables
load_dotenv()


def demo_ioc_extraction():
    """Demonstrate IOC extraction capabilities."""
    print("🔍 IOC Extraction Demo")
    print("-" * 30)
    
    # Set API key for testing
    os.environ["OPENAI_API_KEY"] = os.getenv("OPENAI_API_KEY", "dummy-key")
    
    from agents.cti_agent import CTIAgent
    
    # Create CTI agent without CrewAI integration for this demo
    cti_agent = CTIAgent()
    
    # Sample threat report
    sample_report = """
    Security Alert: Advanced Persistent Threat Campaign Detected
    
    Our threat intelligence team has identified a sophisticated campaign targeting
    financial institutions. The malware communicates with command and control
    servers at the following IP addresses:
    
    - ************* (primary C2)
    - ********* (backup C2)
    - ************ (exfiltration server)
    
    The malicious domains used in this campaign include:
    - malicious-bank-portal.com
    - fake-financial-update.net
    - secure-banking-alert.org
    
    File hashes associated with this threat:
    MD5: 5d41402abc4b2a76b9719d911017c592
    SHA1: aaf4c61ddcc5e8a2dabede0f3b482cd9aea9434d
    SHA256: e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855
    
    Phishing emails were sent from:
    - <EMAIL>
    - <EMAIL>
    
    Additional payload URLs:
    - https://malicious-bank-portal.com/update.exe
    - http://fake-financial-update.net/secure/login.php
    """
    
    print("Sample Threat Report:")
    print(sample_report[:200] + "...")
    print()
    
    # Extract IOCs
    print("Extracting IOCs...")
    iocs = cti_agent.extract_iocs(sample_report)
    
    print("\n📊 Extracted IOCs:")
    for ioc_type, ioc_list in iocs.items():
        if ioc_list:
            print(f"  {ioc_type.replace('_', ' ').title()}: {len(ioc_list)} found")
            for ioc in ioc_list[:3]:  # Show first 3
                print(f"    - {ioc}")
            if len(ioc_list) > 3:
                print(f"    ... and {len(ioc_list) - 3} more")
    
    # Generate IOC report
    print("\n📋 Generating IOC Report...")
    ioc_report = cti_agent.generate_ioc_report(
        iocs=iocs,
        context="IOCs extracted from financial sector threat campaign"
    )
    
    print(f"Report Title: {ioc_report.get('title', 'N/A')}")
    print(f"Total Indicators: {ioc_report.get('summary', {}).get('total_indicators', 0)}")
    print(f"Confidence: {ioc_report.get('confidence', 'N/A')}")
    
    return iocs, ioc_report


def demo_threat_actor_tracking():
    """Demonstrate threat actor tracking."""
    print("\n👤 Threat Actor Tracking Demo")
    print("-" * 30)
    
    from agents.cti_agent import CTIAgent
    
    cti_agent = CTIAgent()
    
    # Track known threat actors
    threat_actors = ["APT28", "Lazarus", "DarkHydrus"]
    
    for actor in threat_actors:
        print(f"\nTracking: {actor}")
        profile = cti_agent.track_threat_actor(actor)
        
        print(f"  Known Aliases: {profile.get('known_aliases', [])}")
        print(f"  Primary Targets: {profile.get('primary_targets', [])}")
        print(f"  Preferred TTPs: {profile.get('preferred_ttps', [])[:2]}...")  # Show first 2


def demo_geopolitical_analysis():
    """Demonstrate geopolitical analysis capabilities."""
    print("\n🌍 Geopolitical Analysis Demo")
    print("-" * 30)
    
    from agents.geo_agent import GeopoliticalAgent
    
    geo_agent = GeopoliticalAgent()
    
    # Sample analysis topics
    topics = [
        "Recent diplomatic developments in Eastern Europe",
        "Trade relations impact on regional stability",
        "Security implications of recent policy changes"
    ]
    
    for topic in topics:
        print(f"\nAnalyzing: {topic}")
        
        # Perform basic analysis (without API calls for demo)
        analysis = geo_agent.analyze(
            query=topic,
            time_range="7d",
            regions=["Europe", "Asia"]
        )
        
        print(f"  Focus Areas: {analysis.get('focus_areas', [])}")
        print(f"  Confidence Level: {analysis.get('confidence_level', 'Unknown')}")
        print(f"  Recommendations: {len(analysis.get('recommendations', []))} provided")


def demo_dspy_evaluation():
    """Demonstrate DSPy evaluation capabilities."""
    print("\n🧪 DSPy Evaluation Demo")
    print("-" * 30)
    
    from workflows.dspy_evaluator import DSPyEvaluator
    
    evaluator = DSPyEvaluator()
    
    # Generate synthetic examples
    print("Generating synthetic evaluation examples...")
    
    geo_examples = evaluator.generate_synthetic_examples(
        domain="geopolitical",
        num_examples=3
    )
    
    cti_examples = evaluator.generate_synthetic_examples(
        domain="cti",
        num_examples=3
    )
    
    print(f"Generated {len(geo_examples)} geopolitical examples")
    print(f"Generated {len(cti_examples)} CTI examples")
    
    # Show sample examples
    print("\nSample Geopolitical Example:")
    if geo_examples:
        example = geo_examples[0]
        print(f"  Query: {example['query']}")
        print(f"  Complexity: {example['complexity']}")
    
    print("\nSample CTI Example:")
    if cti_examples:
        example = cti_examples[0]
        print(f"  Query: {example['query']}")
        print(f"  Complexity: {example['complexity']}")
    
    return geo_examples, cti_examples


def main():
    """Main demo function."""
    print("🧠 CrewAI OSINT Framework - Simple Demo")
    print("=" * 50)
    print(f"Demo started at: {datetime.now().isoformat()}")
    
    # Check for API keys
    if not os.getenv("OPENAI_API_KEY"):
        print("⚠️  Warning: OPENAI_API_KEY not set. Some features may not work.")
    else:
        print("✅ OpenAI API key found")
    
    if not os.getenv("SERPER_API_KEY"):
        print("⚠️  Warning: SERPER_API_KEY not set. Web search features disabled.")
    else:
        print("✅ Serper API key found")
    
    try:
        # Run demos
        iocs, ioc_report = demo_ioc_extraction()
        demo_threat_actor_tracking()
        demo_geopolitical_analysis()
        geo_examples, cti_examples = demo_dspy_evaluation()
        
        # Save demo results
        print("\n💾 Saving Demo Results")
        print("-" * 30)
        
        results = {
            "demo_timestamp": datetime.now().isoformat(),
            "ioc_extraction": {
                "iocs": iocs,
                "report": ioc_report
            },
            "evaluation_examples": {
                "geopolitical": geo_examples,
                "cti": cti_examples
            }
        }
        
        output_file = f"./output/reports/demo_results_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        os.makedirs(os.path.dirname(output_file), exist_ok=True)
        
        with open(output_file, 'w') as f:
            json.dump(results, f, indent=2, default=str)
        
        print(f"Demo results saved to: {output_file}")
        
        print("\n✅ Demo completed successfully!")
        print("\nKey Capabilities Demonstrated:")
        print("1. ✅ IOC Extraction from threat reports")
        print("2. ✅ Threat actor profile tracking")
        print("3. ✅ Geopolitical analysis framework")
        print("4. ✅ DSPy evaluation and synthetic data generation")
        print("5. ✅ Structured reporting and data export")
        
        print("\nNext Steps:")
        print("1. Set up API keys for full functionality")
        print("2. Try the full example scripts")
        print("3. Customize agents for your specific use cases")
        print("4. Build custom workflows and evaluations")
        
        return 0
        
    except Exception as e:
        print(f"\n❌ Demo error: {str(e)}")
        import traceback
        traceback.print_exc()
        return 1


if __name__ == "__main__":
    exit(main())
