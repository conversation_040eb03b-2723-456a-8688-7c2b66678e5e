#!/usr/bin/env python3
"""
Example script demonstrating geopolitical OSINT analysis using the CrewAI framework.

This script shows how to:
1. Initialize a geopolitical agent
2. Perform intelligence analysis
3. Generate intelligence briefs
4. Set up regional monitoring

Usage:
    python examples/geopolitical_analysis_example.py
"""

import os
import sys
import json
from datetime import datetime

# Add project root to path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from agents.geo_agent import GeopoliticalAgent
from workflows.langchain_geo_workflow import GeopoliticalWorkflow
from dotenv import load_dotenv

# Load environment variables
load_dotenv()


def main():
    """Main example function."""
    print("🌍 CrewAI Geopolitical OSINT Analysis Example")
    print("=" * 50)
    
    # Check for required API keys
    if not os.getenv("OPENAI_API_KEY"):
        print("❌ Error: OPENAI_API_KEY environment variable is required")
        print("Please set your OpenAI API key in a .env file")
        return
    
    try:
        # Example 1: Basic Geopolitical Agent Analysis
        print("\n📊 Example 1: Basic Geopolitical Analysis")
        print("-" * 40)
        
        # Initialize the geopolitical agent
        geo_agent = GeopoliticalAgent(verbose=True)
        
        # Perform analysis on a geopolitical topic
        query = "Recent diplomatic tensions between major powers in the South China Sea"
        print(f"Query: {query}")
        
        analysis_result = geo_agent.analyze(
            query=query,
            time_range="7d",
            regions=["Southeast Asia", "East Asia"]
        )
        
        print(f"\n✅ Analysis completed!")
        print(f"Confidence Level: {analysis_result.get('confidence_level', 'Unknown')}")
        print(f"Key Findings: {analysis_result.get('key_findings', 'N/A')[:200]}...")
        
        # Example 2: Intelligence Brief Generation
        print("\n📋 Example 2: Intelligence Brief Generation")
        print("-" * 40)
        
        brief_topic = "NATO expansion and Eastern European security"
        intelligence_brief = geo_agent.generate_intelligence_brief(
            topic=brief_topic,
            classification="UNCLASSIFIED"
        )
        
        print(f"Brief Title: {intelligence_brief.get('title', 'N/A')}")
        print(f"Date: {intelligence_brief.get('date', 'N/A')}")
        print(f"Executive Summary: {intelligence_brief.get('executive_summary', 'N/A')[:200]}...")
        
        # Example 3: Regional Monitoring Setup
        print("\n🔍 Example 3: Regional Monitoring Setup")
        print("-" * 40)
        
        monitoring_config = geo_agent.monitor_region(
            region="Middle East",
            keywords=["conflict", "diplomacy", "security", "oil", "sanctions"]
        )
        
        print(f"Monitoring Region: {monitoring_config.get('region', 'N/A')}")
        print(f"Keywords: {monitoring_config.get('keywords', [])}")
        print(f"Started: {monitoring_config.get('monitoring_started', 'N/A')}")
        
        # Example 4: Workflow-based Analysis
        print("\n🔄 Example 4: Workflow-based Comprehensive Analysis")
        print("-" * 40)
        
        # Initialize workflow
        workflow = GeopoliticalWorkflow(verbose=True)
        
        # Perform comprehensive analysis
        workflow_query = "Impact of recent elections on regional stability in Latin America"
        workflow_result = workflow.analyze_geopolitical_situation(
            topic=workflow_query,
            regions=["Latin America"],
            time_range="30d",
            include_historical_context=True
        )
        
        print(f"Workflow Analysis completed!")
        print(f"Confidence: {workflow_result.get('confidence_level', 'Unknown')}")
        print(f"Report length: {len(workflow_result.get('final_report', ''))} characters")
        
        # Example 5: Multi-Region Situation Report
        print("\n🌐 Example 5: Multi-Region Situation Report")
        print("-" * 40)
        
        situation_report = workflow.generate_situation_report(
            regions=["Eastern Europe", "Middle East", "Southeast Asia"],
            time_range="24h"
        )
        
        print(f"Regions analyzed: {situation_report.get('regions', [])}")
        print(f"Report generated at: {situation_report.get('generated_at', 'N/A')}")
        
        # Save results to file
        print("\n💾 Saving Results")
        print("-" * 40)
        
        results = {
            "basic_analysis": analysis_result,
            "intelligence_brief": intelligence_brief,
            "monitoring_config": monitoring_config,
            "workflow_analysis": workflow_result,
            "situation_report": situation_report,
            "generated_at": datetime.now().isoformat()
        }
        
        output_file = f"./output/reports/geopolitical_analysis_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        os.makedirs(os.path.dirname(output_file), exist_ok=True)
        
        with open(output_file, 'w') as f:
            json.dump(results, f, indent=2, default=str)
        
        print(f"Results saved to: {output_file}")
        
        # Display agent capabilities
        print("\n🔧 Agent Capabilities")
        print("-" * 40)
        
        capabilities = geo_agent.get_capabilities()
        print(f"Agent Name: {capabilities.get('name', 'N/A')}")
        print(f"Role: {capabilities.get('role', 'N/A')}")
        print(f"Available Tools: {capabilities.get('tools', [])}")
        print(f"Has Knowledge Base: {capabilities.get('has_knowledge_base', False)}")
        
        print("\n✅ Geopolitical analysis example completed successfully!")
        print("\nNext steps:")
        print("1. Review the generated reports in the output directory")
        print("2. Experiment with different queries and regions")
        print("3. Set up continuous monitoring for areas of interest")
        print("4. Integrate with your existing intelligence workflows")
        
    except Exception as e:
        print(f"\n❌ Error during analysis: {str(e)}")
        print("Please check your configuration and try again.")
        return 1
    
    return 0


if __name__ == "__main__":
    exit(main())
