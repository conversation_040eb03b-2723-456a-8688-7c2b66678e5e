# 🚀 Quick Start Guide - CrewAI OSINT Framework

## 🎯 What We've Built

You now have a comprehensive **OSINT (Open Source Intelligence) framework** that combines:
- **CrewAI** for multi-agent coordination
- **<PERSON><PERSON>hain** for workflow orchestration  
- **LlamaIndex** for advanced RAG (Retrieval-Augmented Generation)
- **DSPy** for prompt optimization and evaluation

## 🏗️ Framework Components

### 🤖 **Two Specialized Agents**
1. **Geopolitical Analyst** - International relations, conflicts, political developments
2. **Cyber Threat Intelligence Analyst** - Threat hunting, IOC extraction, APT tracking

### 🔧 **Core Capabilities**
- **Web Intelligence Gathering** - Automated search and content extraction
- **Document Analysis** - Vector-based semantic search and indexing
- **IOC Extraction** - Automatic identification of cyber threat indicators
- **Intelligence Reporting** - Structured analytical products
- **Performance Evaluation** - DSPy-powered optimization

## 🚀 **Getting Started**

### 1. **Environment Setup** (Already Done!)
```bash
# Virtual environment created and dependencies installed
source venv/bin/activate
```

### 2. **API Keys** (Already Configured!)
Your API keys are set up in `.env`:
- ✅ OpenAI API Key (for LLM capabilities)
- ✅ Serper API Key (for web search)

### 3. **Test the Framework**
```bash
# Run basic functionality test
python test_basic.py

# Run comprehensive demo
python examples/simple_demo.py

# Try specific examples
python examples/geopolitical_analysis_example.py
python examples/cti_analysis_example.py
python examples/dspy_evaluation_example.py
```

## 📊 **What You Can Do Right Now**

### 🔍 **Cyber Threat Intelligence**
```python
from agents.cti_agent import CTIAgent

cti_agent = CTIAgent()

# Extract IOCs from threat reports
iocs = cti_agent.extract_iocs(threat_report_text)

# Track threat actors
profile = cti_agent.track_threat_actor("APT28")

# Generate IOC reports
report = cti_agent.generate_ioc_report(iocs, "Campaign context")
```

### 🌍 **Geopolitical Analysis**
```python
from agents.geo_agent import GeopoliticalAgent

geo_agent = GeopoliticalAgent()

# Analyze geopolitical situations
analysis = geo_agent.analyze(
    query="Recent diplomatic tensions in Eastern Europe",
    time_range="7d",
    regions=["Eastern Europe"]
)

# Generate intelligence briefs
brief = geo_agent.generate_intelligence_brief(
    topic="NATO expansion implications"
)
```

### 🧪 **Evaluation and Optimization**
```python
from workflows.dspy_evaluator import DSPyEvaluator

evaluator = DSPyEvaluator()

# Generate synthetic test data
examples = evaluator.generate_synthetic_examples(
    domain="geopolitical", 
    num_examples=10
)

# Evaluate analysis quality
evaluation = evaluator.evaluate_analysis_quality(
    domain="cti",
    query="Threat analysis query",
    analysis="Generated analysis"
)
```

## 📁 **Key Files and Directories**

```
📦 Your OSINT Framework
├── 🤖 agents/              # Specialized OSINT agents
│   ├── geo_agent.py       # Geopolitical analyst
│   └── cti_agent.py       # Cyber threat intelligence
├── 🔧 tools/              # External service wrappers
│   ├── serper_wrapper.py  # Google Search API
│   ├── crawl4ai_wrapper.py # Web crawling
│   └── llamaindex_tools.py # RAG capabilities
├── 📚 rag/                # Document processing
│   ├── index_builder.py   # Vector indexing
│   └── retriever.py       # Advanced search
├── 🔄 workflows/          # Analysis workflows
│   ├── langchain_geo_workflow.py
│   ├── llamaindex_cti_workflow.py
│   └── dspy_evaluator.py
├── 📖 examples/           # Usage demonstrations
├── 📊 output/reports/     # Generated intelligence
└── 📋 data/              # Indexed documents
```

## 🎯 **Immediate Use Cases**

### 1. **Threat Intelligence Processing**
- Upload threat reports → Extract IOCs → Generate actionable intelligence
- Track APT groups and their evolving tactics
- Correlate indicators across multiple campaigns

### 2. **Geopolitical Monitoring**
- Monitor regional developments and diplomatic changes
- Generate situation reports for decision makers
- Assess policy implications and security risks

### 3. **Research and Analysis**
- Build knowledge bases from web sources
- Perform semantic search across intelligence documents
- Generate synthetic data for testing and evaluation

## 🔧 **Customization Options**

### **Add New Data Sources**
```python
# Ingest documents into RAG system
from rag.index_builder import IndexBuilder

builder = IndexBuilder()
index = builder.build_index(
    documents=your_documents,
    urls=your_urls,
    file_paths=your_files
)
```

### **Create Custom Workflows**
```python
# Build specialized analysis pipelines
from workflows.langchain_geo_workflow import GeopoliticalWorkflow

workflow = GeopoliticalWorkflow()
result = workflow.analyze_geopolitical_situation(
    topic="Your analysis topic",
    regions=["Your regions"],
    time_range="30d"
)
```

### **Optimize Performance**
```python
# Use DSPy to improve prompts and analysis
from workflows.dspy_evaluator import DSPyEvaluator

evaluator = DSPyEvaluator()
optimized_module, results = evaluator.optimize_prompts(
    domain="your_domain",
    module_to_optimize=your_module
)
```

## 🚀 **Next Steps for Enhancement**

### **Immediate (Ready to Use)**
1. **Run the examples** to see capabilities
2. **Process your own data** through the RAG pipeline
3. **Generate intelligence reports** for your use cases
4. **Experiment with DSPy optimization**

### **Short Term (Easy to Add)**
1. **Add more data sources** (RSS feeds, APIs, databases)
2. **Create custom prompts** for your specific domains
3. **Build specialized workflows** for your organization
4. **Integrate with existing tools** via the API framework

### **Long Term (Advanced Features)**
1. **Web UI development** with Streamlit
2. **Real-time monitoring** and alerting
3. **Advanced ML models** for attribution and prediction
4. **Multi-modal analysis** (images, videos, documents)

## 📞 **Support and Documentation**

- **📖 Full Documentation**: See `README.md`
- **📊 Implementation Status**: See `IMPLEMENTATION_STATUS.md`
- **🔧 Examples**: Check the `examples/` directory
- **🧪 Testing**: Run `test_basic.py` for validation

## 🎉 **You're Ready to Go!**

Your OSINT framework is **fully functional** and ready for:
- ✅ Threat intelligence analysis
- ✅ Geopolitical monitoring  
- ✅ Document processing and search
- ✅ Intelligence report generation
- ✅ Performance optimization

**Start with the examples and customize for your specific intelligence requirements!**
