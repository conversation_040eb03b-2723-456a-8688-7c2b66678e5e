"""
Document retriever for advanced search and filtering capabilities.
"""

import logging
from typing import List, Dict, Any, Optional, Tuple
from datetime import datetime, timedelta

from llama_index.core import VectorStoreIndex, QueryBundle
from llama_index.core.retrievers import VectorIndexRetriever
from llama_index.core.query_engine import RetrieverQueryEngine
from llama_index.core.postprocessor import SimilarityPostprocessor, KeywordNodePostprocessor
from llama_index.core.response_synthesizers import ResponseMode


class DocumentRetriever:
    """
    Advanced document retriever with filtering and ranking capabilities.
    
    Provides sophisticated search functionality including:
    - Semantic similarity search
    - Keyword filtering
    - Metadata-based filtering
    - Time-based filtering
    - Hybrid search combining multiple strategies
    """
    
    def __init__(self, index: VectorStoreIndex):
        """
        Initialize the document retriever.
        
        Args:
            index: VectorStoreIndex to search
        """
        self.index = index
        self.logger = logging.getLogger(__name__)
        
        # Initialize base retriever
        self.base_retriever = VectorIndexRetriever(
            index=index,
            similarity_top_k=20  # Retrieve more initially for filtering
        )
    
    def semantic_search(
        self,
        query: str,
        top_k: int = 10,
        similarity_threshold: float = 0.7,
        metadata_filters: Dict[str, Any] = None
    ) -> List[Dict[str, Any]]:
        """
        Perform semantic similarity search.
        
        Args:
            query: Search query
            top_k: Number of results to return
            similarity_threshold: Minimum similarity score
            metadata_filters: Optional metadata filters
            
        Returns:
            List of search results with metadata
        """
        try:
            # Create query bundle
            query_bundle = QueryBundle(query_str=query)
            
            # Retrieve nodes
            nodes = self.base_retriever.retrieve(query_bundle)
            
            # Filter and process results
            results = []
            for node in nodes:
                # Check similarity threshold
                score = float(node.score) if hasattr(node, 'score') else 0.0
                if score < similarity_threshold:
                    continue
                
                # Apply metadata filters
                if metadata_filters and not self._matches_metadata_filters(
                    node.node.metadata, metadata_filters
                ):
                    continue
                
                result = {
                    "content": node.node.text,
                    "score": score,
                    "metadata": node.node.metadata,
                    "node_id": node.node.node_id if hasattr(node.node, 'node_id') else None
                }
                results.append(result)
            
            # Sort by score and limit results
            results.sort(key=lambda x: x["score"], reverse=True)
            return results[:top_k]
            
        except Exception as e:
            self.logger.error(f"Error in semantic search: {str(e)}")
            return []
    
    def keyword_search(
        self,
        keywords: List[str],
        top_k: int = 10,
        require_all: bool = False,
        metadata_filters: Dict[str, Any] = None
    ) -> List[Dict[str, Any]]:
        """
        Perform keyword-based search.
        
        Args:
            keywords: List of keywords to search for
            top_k: Number of results to return
            require_all: Whether all keywords must be present
            metadata_filters: Optional metadata filters
            
        Returns:
            List of search results
        """
        try:
            # Create keyword processor
            keyword_processor = KeywordNodePostprocessor(
                keywords=keywords,
                required_keywords=keywords if require_all else None
            )
            
            # Get all documents for keyword filtering
            # Use a broad query to get candidates
            query_bundle = QueryBundle(query_str=" ".join(keywords))
            nodes = self.base_retriever.retrieve(query_bundle)
            
            # Apply keyword filtering
            filtered_nodes = keyword_processor.postprocess_nodes(nodes)
            
            # Process results
            results = []
            for node in filtered_nodes:
                # Apply metadata filters
                if metadata_filters and not self._matches_metadata_filters(
                    node.node.metadata, metadata_filters
                ):
                    continue
                
                # Calculate keyword match score
                keyword_score = self._calculate_keyword_score(node.node.text, keywords)
                
                result = {
                    "content": node.node.text,
                    "score": keyword_score,
                    "metadata": node.node.metadata,
                    "matched_keywords": self._find_matched_keywords(node.node.text, keywords),
                    "node_id": node.node.node_id if hasattr(node.node, 'node_id') else None
                }
                results.append(result)
            
            # Sort by keyword score and limit results
            results.sort(key=lambda x: x["score"], reverse=True)
            return results[:top_k]
            
        except Exception as e:
            self.logger.error(f"Error in keyword search: {str(e)}")
            return []
    
    def hybrid_search(
        self,
        query: str,
        keywords: List[str] = None,
        top_k: int = 10,
        semantic_weight: float = 0.7,
        keyword_weight: float = 0.3,
        metadata_filters: Dict[str, Any] = None
    ) -> List[Dict[str, Any]]:
        """
        Perform hybrid search combining semantic and keyword search.
        
        Args:
            query: Semantic search query
            keywords: Optional keywords for keyword search
            top_k: Number of results to return
            semantic_weight: Weight for semantic scores
            keyword_weight: Weight for keyword scores
            metadata_filters: Optional metadata filters
            
        Returns:
            List of hybrid search results
        """
        try:
            # Perform semantic search
            semantic_results = self.semantic_search(
                query=query,
                top_k=top_k * 2,  # Get more results for merging
                similarity_threshold=0.5,  # Lower threshold for hybrid
                metadata_filters=metadata_filters
            )
            
            # Perform keyword search if keywords provided
            keyword_results = []
            if keywords:
                keyword_results = self.keyword_search(
                    keywords=keywords,
                    top_k=top_k * 2,
                    metadata_filters=metadata_filters
                )
            
            # Merge and score results
            merged_results = self._merge_search_results(
                semantic_results,
                keyword_results,
                semantic_weight,
                keyword_weight
            )
            
            # Sort by combined score and limit results
            merged_results.sort(key=lambda x: x["combined_score"], reverse=True)
            return merged_results[:top_k]
            
        except Exception as e:
            self.logger.error(f"Error in hybrid search: {str(e)}")
            return []
    
    def time_filtered_search(
        self,
        query: str,
        start_date: datetime = None,
        end_date: datetime = None,
        date_field: str = "created_date",
        top_k: int = 10
    ) -> List[Dict[str, Any]]:
        """
        Perform search with time-based filtering.
        
        Args:
            query: Search query
            start_date: Start date for filtering
            end_date: End date for filtering
            date_field: Metadata field containing date information
            top_k: Number of results to return
            
        Returns:
            List of time-filtered search results
        """
        try:
            # Create time-based metadata filter
            metadata_filters = {}
            if start_date or end_date:
                metadata_filters[date_field] = {
                    "start": start_date,
                    "end": end_date
                }
            
            # Perform semantic search with time filters
            results = self.semantic_search(
                query=query,
                top_k=top_k,
                metadata_filters=metadata_filters
            )
            
            return results
            
        except Exception as e:
            self.logger.error(f"Error in time-filtered search: {str(e)}")
            return []
    
    def get_similar_documents(
        self,
        document_id: str,
        top_k: int = 10,
        similarity_threshold: float = 0.7
    ) -> List[Dict[str, Any]]:
        """
        Find documents similar to a given document.
        
        Args:
            document_id: ID of the reference document
            top_k: Number of similar documents to return
            similarity_threshold: Minimum similarity score
            
        Returns:
            List of similar documents
        """
        try:
            # Get the reference document
            ref_doc = self.index.docstore.get_document(document_id)
            if not ref_doc:
                self.logger.warning(f"Document not found: {document_id}")
                return []
            
            # Use document text as query for similarity search
            query = ref_doc.text[:1000]  # Use first 1000 chars as query
            
            results = self.semantic_search(
                query=query,
                top_k=top_k + 1,  # +1 to account for the reference doc itself
                similarity_threshold=similarity_threshold
            )
            
            # Remove the reference document from results
            filtered_results = [
                result for result in results 
                if result.get("node_id") != document_id
            ]
            
            return filtered_results[:top_k]
            
        except Exception as e:
            self.logger.error(f"Error finding similar documents: {str(e)}")
            return []
    
    def _matches_metadata_filters(
        self, 
        metadata: Dict[str, Any], 
        filters: Dict[str, Any]
    ) -> bool:
        """Check if metadata matches the provided filters."""
        for key, filter_value in filters.items():
            if key not in metadata:
                return False
            
            metadata_value = metadata[key]
            
            # Handle different filter types
            if isinstance(filter_value, dict):
                # Range filter (e.g., for dates)
                if "start" in filter_value or "end" in filter_value:
                    if not self._matches_range_filter(metadata_value, filter_value):
                        return False
            elif isinstance(filter_value, list):
                # List filter (value must be in list)
                if metadata_value not in filter_value:
                    return False
            else:
                # Exact match filter
                if metadata_value != filter_value:
                    return False
        
        return True
    
    def _matches_range_filter(self, value: Any, range_filter: Dict[str, Any]) -> bool:
        """Check if a value matches a range filter."""
        try:
            # Convert value to datetime if it's a string
            if isinstance(value, str):
                value = datetime.fromisoformat(value.replace('Z', '+00:00'))
            
            start_date = range_filter.get("start")
            end_date = range_filter.get("end")
            
            if start_date and value < start_date:
                return False
            if end_date and value > end_date:
                return False
            
            return True
            
        except Exception:
            return False
    
    def _calculate_keyword_score(self, text: str, keywords: List[str]) -> float:
        """Calculate keyword match score for a text."""
        text_lower = text.lower()
        total_matches = 0
        
        for keyword in keywords:
            keyword_lower = keyword.lower()
            matches = text_lower.count(keyword_lower)
            total_matches += matches
        
        # Normalize by text length and number of keywords
        word_count = len(text.split())
        if word_count == 0:
            return 0.0
        
        score = (total_matches / word_count) * len(keywords)
        return min(score, 1.0)  # Cap at 1.0
    
    def _find_matched_keywords(self, text: str, keywords: List[str]) -> List[str]:
        """Find which keywords are present in the text."""
        text_lower = text.lower()
        matched = []
        
        for keyword in keywords:
            if keyword.lower() in text_lower:
                matched.append(keyword)
        
        return matched
    
    def _merge_search_results(
        self,
        semantic_results: List[Dict[str, Any]],
        keyword_results: List[Dict[str, Any]],
        semantic_weight: float,
        keyword_weight: float
    ) -> List[Dict[str, Any]]:
        """Merge semantic and keyword search results."""
        # Create a map of node_id to results for deduplication
        result_map = {}
        
        # Add semantic results
        for result in semantic_results:
            node_id = result.get("node_id", result["content"][:50])
            result_map[node_id] = {
                **result,
                "semantic_score": result["score"],
                "keyword_score": 0.0,
                "combined_score": result["score"] * semantic_weight
            }
        
        # Add/merge keyword results
        for result in keyword_results:
            node_id = result.get("node_id", result["content"][:50])
            
            if node_id in result_map:
                # Update existing result
                result_map[node_id]["keyword_score"] = result["score"]
                result_map[node_id]["combined_score"] = (
                    result_map[node_id]["semantic_score"] * semantic_weight +
                    result["score"] * keyword_weight
                )
                if "matched_keywords" in result:
                    result_map[node_id]["matched_keywords"] = result["matched_keywords"]
            else:
                # Add new result
                result_map[node_id] = {
                    **result,
                    "semantic_score": 0.0,
                    "keyword_score": result["score"],
                    "combined_score": result["score"] * keyword_weight
                }
        
        return list(result_map.values())
