"""
Vector index builder for document ingestion and indexing.
"""

import os
import logging
from typing import List, Dict, Any, Optional
from pathlib import Path

from llama_index.core import (
    VectorStoreIndex, 
    Document, 
    StorageContext,
    load_index_from_storage,
    Settings
)
from llama_index.embeddings.openai import OpenAIEmbedding
from llama_index.llms.openai import OpenAI
from llama_index.core.node_parser import <PERSON><PERSON><PERSON><PERSON>plitter
from llama_index.core.extractors import TitleExtractor, QuestionsAnsweredExtractor

try:
    import requests
    from bs4 import BeautifulSoup
    WEB_SCRAPING_AVAILABLE = True
except ImportError:
    WEB_SCRAPING_AVAILABLE = False


class IndexBuilder:
    """
    Builds and manages vector indexes for document retrieval.
    
    Supports multiple document sources including text files, URLs,
    and direct text input. Provides persistent storage and loading
    of indexes.
    """
    
    def __init__(
        self,
        storage_dir: str = "./data/vector_index",
        embedding_model: str = "text-embedding-ada-002",
        llm_model: str = "gpt-3.5-turbo",
        chunk_size: int = 1024,
        chunk_overlap: int = 200
    ):
        """
        Initialize the index builder.
        
        Args:
            storage_dir: Directory to store the vector index
            embedding_model: OpenAI embedding model to use
            llm_model: LLM model for processing
            chunk_size: Size of text chunks for indexing
            chunk_overlap: Overlap between chunks
        """
        self.storage_dir = Path(storage_dir)
        self.storage_dir.mkdir(parents=True, exist_ok=True)
        
        # Configure LlamaIndex settings
        Settings.llm = OpenAI(model=llm_model, temperature=0.1)
        Settings.embed_model = OpenAIEmbedding(model=embedding_model)
        
        # Initialize text splitter
        self.text_splitter = SentenceSplitter(
            chunk_size=chunk_size,
            chunk_overlap=chunk_overlap
        )
        
        # Initialize extractors for metadata
        self.extractors = [
            TitleExtractor(nodes=5),
            QuestionsAnsweredExtractor(questions=3)
        ]
        
        self.logger = logging.getLogger(__name__)
    
    def build_index(
        self,
        documents: List[Document] = None,
        urls: List[str] = None,
        file_paths: List[str] = None,
        texts: List[str] = None,
        metadata_list: List[Dict[str, Any]] = None
    ) -> VectorStoreIndex:
        """
        Build a vector index from various document sources.
        
        Args:
            documents: List of LlamaIndex Document objects
            urls: List of URLs to crawl and index
            file_paths: List of file paths to read and index
            texts: List of text strings to index
            metadata_list: Optional metadata for each text
            
        Returns:
            VectorStoreIndex instance
        """
        all_documents = []
        
        # Add provided documents
        if documents:
            all_documents.extend(documents)
        
        # Process URLs
        if urls and WEB_SCRAPING_AVAILABLE:
            url_docs = self._process_urls(urls)
            all_documents.extend(url_docs)
        elif urls and not WEB_SCRAPING_AVAILABLE:
            self.logger.warning("Web scraping not available. Install requests and beautifulsoup4.")
        
        # Process file paths
        if file_paths:
            file_docs = self._process_files(file_paths)
            all_documents.extend(file_docs)
        
        # Process text strings
        if texts:
            text_docs = self._process_texts(texts, metadata_list)
            all_documents.extend(text_docs)
        
        if not all_documents:
            raise ValueError("No documents provided for indexing")
        
        self.logger.info(f"Building index with {len(all_documents)} documents")
        
        # Build the index
        index = VectorStoreIndex.from_documents(
            all_documents,
            transformations=[self.text_splitter] + self.extractors,
            show_progress=True
        )
        
        return index
    
    def _process_urls(self, urls: List[str]) -> List[Document]:
        """Process URLs and extract content."""
        documents = []
        
        for url in urls:
            try:
                self.logger.info(f"Processing URL: {url}")
                
                # Fetch content
                response = requests.get(url, timeout=30)
                response.raise_for_status()
                
                # Parse HTML
                soup = BeautifulSoup(response.content, 'html.parser')
                
                # Extract text content
                # Remove script and style elements
                for script in soup(["script", "style"]):
                    script.decompose()
                
                text = soup.get_text()
                
                # Clean up text
                lines = (line.strip() for line in text.splitlines())
                chunks = (phrase.strip() for line in lines for phrase in line.split("  "))
                text = ' '.join(chunk for chunk in chunks if chunk)
                
                # Create document with metadata
                metadata = {
                    "source": url,
                    "source_type": "url",
                    "title": soup.title.string if soup.title else "",
                    "content_length": len(text)
                }
                
                doc = Document(text=text, metadata=metadata)
                documents.append(doc)
                
            except Exception as e:
                self.logger.error(f"Error processing URL {url}: {str(e)}")
                continue
        
        return documents
    
    def _process_files(self, file_paths: List[str]) -> List[Document]:
        """Process files and extract content."""
        documents = []
        
        for file_path in file_paths:
            try:
                path = Path(file_path)
                if not path.exists():
                    self.logger.warning(f"File not found: {file_path}")
                    continue
                
                self.logger.info(f"Processing file: {file_path}")
                
                # Read file content
                with open(path, 'r', encoding='utf-8') as f:
                    text = f.read()
                
                # Create document with metadata
                metadata = {
                    "source": str(path),
                    "source_type": "file",
                    "filename": path.name,
                    "file_size": path.stat().st_size,
                    "content_length": len(text)
                }
                
                doc = Document(text=text, metadata=metadata)
                documents.append(doc)
                
            except Exception as e:
                self.logger.error(f"Error processing file {file_path}: {str(e)}")
                continue
        
        return documents
    
    def _process_texts(
        self, 
        texts: List[str], 
        metadata_list: List[Dict[str, Any]] = None
    ) -> List[Document]:
        """Process text strings into documents."""
        documents = []
        
        for i, text in enumerate(texts):
            # Get metadata for this text
            metadata = {
                "source": f"text_{i}",
                "source_type": "text",
                "content_length": len(text)
            }
            
            # Add custom metadata if provided
            if metadata_list and i < len(metadata_list):
                metadata.update(metadata_list[i])
            
            doc = Document(text=text, metadata=metadata)
            documents.append(doc)
        
        return documents
    
    def save_index(self, index: VectorStoreIndex, index_name: str = "default") -> str:
        """
        Save an index to persistent storage.
        
        Args:
            index: VectorStoreIndex to save
            index_name: Name for the saved index
            
        Returns:
            Path to saved index
        """
        index_path = self.storage_dir / index_name
        index_path.mkdir(exist_ok=True)
        
        # Persist the index
        index.storage_context.persist(persist_dir=str(index_path))
        
        self.logger.info(f"Index saved to: {index_path}")
        return str(index_path)
    
    def load_index(self, index_name: str = "default") -> Optional[VectorStoreIndex]:
        """
        Load an index from persistent storage.
        
        Args:
            index_name: Name of the index to load
            
        Returns:
            VectorStoreIndex instance or None if not found
        """
        index_path = self.storage_dir / index_name
        
        if not index_path.exists():
            self.logger.warning(f"Index not found: {index_path}")
            return None
        
        try:
            # Load the storage context
            storage_context = StorageContext.from_defaults(persist_dir=str(index_path))
            
            # Load the index
            index = load_index_from_storage(storage_context)
            
            self.logger.info(f"Index loaded from: {index_path}")
            return index
            
        except Exception as e:
            self.logger.error(f"Error loading index: {str(e)}")
            return None
    
    def list_indexes(self) -> List[str]:
        """List available saved indexes."""
        if not self.storage_dir.exists():
            return []
        
        indexes = []
        for item in self.storage_dir.iterdir():
            if item.is_dir() and (item / "docstore.json").exists():
                indexes.append(item.name)
        
        return indexes
    
    def delete_index(self, index_name: str) -> bool:
        """
        Delete a saved index.
        
        Args:
            index_name: Name of the index to delete
            
        Returns:
            True if deleted successfully, False otherwise
        """
        index_path = self.storage_dir / index_name
        
        if not index_path.exists():
            self.logger.warning(f"Index not found: {index_path}")
            return False
        
        try:
            import shutil
            shutil.rmtree(index_path)
            self.logger.info(f"Index deleted: {index_path}")
            return True
            
        except Exception as e:
            self.logger.error(f"Error deleting index: {str(e)}")
            return False
    
    def get_index_info(self, index_name: str = "default") -> Dict[str, Any]:
        """
        Get information about a saved index.
        
        Args:
            index_name: Name of the index
            
        Returns:
            Dictionary with index information
        """
        index_path = self.storage_dir / index_name
        
        if not index_path.exists():
            return {"error": "Index not found"}
        
        try:
            # Load index to get statistics
            index = self.load_index(index_name)
            if not index:
                return {"error": "Failed to load index"}
            
            # Get basic statistics
            info = {
                "name": index_name,
                "path": str(index_path),
                "created": index_path.stat().st_ctime,
                "size_bytes": sum(f.stat().st_size for f in index_path.rglob('*') if f.is_file()),
                "num_documents": len(index.docstore.docs) if hasattr(index, 'docstore') else 0
            }
            
            return info
            
        except Exception as e:
            return {"error": f"Error getting index info: {str(e)}"}
    
    def update_index(
        self, 
        index: VectorStoreIndex, 
        new_documents: List[Document]
    ) -> VectorStoreIndex:
        """
        Update an existing index with new documents.
        
        Args:
            index: Existing VectorStoreIndex
            new_documents: New documents to add
            
        Returns:
            Updated VectorStoreIndex
        """
        self.logger.info(f"Updating index with {len(new_documents)} new documents")
        
        # Add new documents to the index
        for doc in new_documents:
            index.insert(doc)
        
        return index
