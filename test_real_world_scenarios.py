#!/usr/bin/env python3
"""
Real-world scenario testing for the CrewAI OSINT framework.
Tests actual web search and analysis capabilities.
"""

import os
import sys
import json
from datetime import datetime

# Add project root to path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from agents.geo_agent import GeopoliticalAgent
from agents.cti_agent import CTIAgent
from workflows.langchain_geo_workflow import GeopoliticalWorkflow
from workflows.llamaindex_cti_workflow import CTIWorkflow


def test_real_geopolitical_analysis():
    """Test real geopolitical analysis with web search."""
    print("🌍 Testing Real Geopolitical Analysis")
    print("=" * 50)
    
    geo_agent = GeopoliticalAgent()
    
    # Test current events analysis
    query = "Recent developments in Ukraine conflict and NATO response"
    print(f"Query: {query}")
    
    try:
        result = geo_agent.analyze(query, time_range="7d")
        print(f"✅ Analysis completed")
        print(f"Confidence: {result.get('confidence_level', 'Unknown')}")
        print(f"Key findings: {result.get('key_findings', 'N/A')[:200]}...")
        return True
    except Exception as e:
        print(f"❌ Error: {str(e)}")
        return False


def test_real_cti_analysis():
    """Test real CTI analysis with threat intelligence."""
    print("\n🔒 Testing Real CTI Analysis")
    print("=" * 50)
    
    cti_agent = CTIAgent()
    
    # Test current threat analysis
    query = "Recent ransomware attacks targeting healthcare organizations"
    print(f"Query: {query}")
    
    try:
        result = cti_agent.analyze(query, time_range="30d")
        print(f"✅ Analysis completed")
        print(f"Threat type: {result.get('threat_type', 'Unknown')}")
        print(f"Attribution: {result.get('attribution', 'N/A')[:200]}...")
        return True
    except Exception as e:
        print(f"❌ Error: {str(e)}")
        return False


def test_web_search_integration():
    """Test web search tool integration."""
    print("\n🔍 Testing Web Search Integration")
    print("=" * 50)
    
    try:
        from tools.serper_wrapper import SerperSearchTool
        
        search_tool = SerperSearchTool()
        
        # Test news search
        query = "cybersecurity threats 2024"
        print(f"Searching for: {query}")
        
        result = search_tool._run(
            query=query,
            search_type="news",
            num_results=3,
            time_range="w1"
        )
        
        result_data = json.loads(result)
        print(f"✅ Found {len(result_data.get('results', []))} results")
        
        if result_data.get('results'):
            first_result = result_data['results'][0]
            print(f"Top result: {first_result.get('title', 'N/A')[:100]}...")
        
        return True
    except Exception as e:
        print(f"❌ Error: {str(e)}")
        return False


def test_workflow_integration():
    """Test full workflow integration."""
    print("\n🔄 Testing Workflow Integration")
    print("=" * 50)
    
    try:
        geo_workflow = GeopoliticalWorkflow()
        
        # Test comprehensive analysis
        topic = "China-Taiwan tensions and regional implications"
        print(f"Analyzing: {topic}")
        
        result = geo_workflow.analyze_geopolitical_situation(
            topic=topic,
            regions=["East Asia", "Taiwan Strait"],
            time_range="14d",
            include_historical_context=False  # Skip to avoid rate limits
        )
        
        if "error" not in result:
            print(f"✅ Workflow analysis completed")
            print(f"Confidence: {result.get('confidence_level', 'Unknown')}")
            print(f"Report length: {len(str(result.get('final_report', '')))} characters")
        else:
            print(f"⚠️ Workflow completed with error: {result['error']}")
        
        return True
    except Exception as e:
        print(f"❌ Error: {str(e)}")
        return False


def test_ioc_extraction_real():
    """Test IOC extraction with realistic threat data."""
    print("\n🎯 Testing Real IOC Extraction")
    print("=" * 50)
    
    cti_agent = CTIAgent()
    
    # Realistic threat report sample
    threat_report = """
    THREAT ALERT: APT29 Campaign Targeting Government Entities
    
    Our threat intelligence team has identified a sophisticated spear-phishing campaign
    attributed to APT29 (Cozy Bear) targeting government entities across Europe and North America.
    
    The campaign utilizes compromised infrastructure including:
    - Command and control server: 185.159.158.240
    - Malicious domain: secure-gov-portal.net
    - Backup C2: **************
    
    Malware samples identified:
    - SHA256: 3c4e0d2a5b7f8e9d1c6a4b8f2e5d7c9a1b4e6f8a2c5d8e1b4f7a9c2e5d8b1f4a7
    - MD5: a1b2c3d4e5f6789012345678901234567
    
    Phishing infrastructure:
    - Email sender: <EMAIL>
    - Landing page: https://secure-gov-portal.net/login/verify.php
    - Payload delivery: https://cdn-updates.gov-alerts.org/security_patch.exe
    
    The threat actors are using legitimate-looking government email addresses
    and domains to increase the success rate of their phishing campaigns.
    """
    
    try:
        iocs = cti_agent.extract_iocs(threat_report)
        
        total_iocs = sum(len(v) for v in iocs.values())
        print(f"✅ Extracted {total_iocs} IOCs")
        
        for ioc_type, indicators in iocs.items():
            if indicators:
                print(f"  {ioc_type}: {len(indicators)} found")
                for indicator in indicators[:2]:  # Show first 2
                    print(f"    - {indicator}")
                if len(indicators) > 2:
                    print(f"    ... and {len(indicators) - 2} more")
        
        # Generate IOC report
        report = cti_agent.generate_ioc_report(iocs, "APT29 Government Targeting Campaign")
        print(f"✅ IOC report generated: {len(str(report))} characters")
        
        return True
    except Exception as e:
        print(f"❌ Error: {str(e)}")
        return False


def main():
    """Run all real-world scenario tests."""
    print("🧠 CrewAI OSINT Framework - Real-World Scenario Testing")
    print("=" * 70)
    print(f"Test started at: {datetime.now().isoformat()}")
    
    # Check API keys
    if not os.getenv("OPENAI_API_KEY"):
        print("❌ OPENAI_API_KEY not found. Please set your API key.")
        return 1
    
    if not os.getenv("SERPER_API_KEY"):
        print("⚠️ SERPER_API_KEY not found. Web search tests will be skipped.")
    
    tests = [
        ("IOC Extraction (Real Data)", test_ioc_extraction_real),
        ("Geopolitical Analysis", test_real_geopolitical_analysis),
        ("CTI Analysis", test_real_cti_analysis),
    ]
    
    # Only add web search test if API key is available
    if os.getenv("SERPER_API_KEY"):
        tests.append(("Web Search Integration", test_web_search_integration))
        tests.append(("Workflow Integration", test_workflow_integration))
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        try:
            if test_func():
                passed += 1
                print(f"✅ {test_name} PASSED")
            else:
                print(f"❌ {test_name} FAILED")
        except Exception as e:
            print(f"❌ {test_name} FAILED with exception: {str(e)}")
    
    print(f"\n{'='*70}")
    print(f"Real-World Test Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All real-world tests passed! Framework is production-ready.")
        return 0
    else:
        print("⚠️ Some tests failed. Check the errors above.")
        return 1


if __name__ == "__main__":
    exit(main())
