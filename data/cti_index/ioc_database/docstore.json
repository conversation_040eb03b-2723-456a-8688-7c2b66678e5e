{"docstore/metadata": {"0ab31f12-cc22-4821-82d4-dd1c81e4a8eb": {"doc_hash": "182ba211936ace47b514e099a17f5c4b903502afd6f13e58581eae257bb93b38"}, "fd570d6b-40f9-44a4-a136-0de7854c266d": {"doc_hash": "6a38be00048186c78b7d95430f6ff5fb23dda8dc709de1d8cb44c4e30d43f9bb"}, "1ce6f421-bf9e-47d1-9e66-243c163c1859": {"doc_hash": "2ec0c17c760f835e9ab6eaf5f2606a5216676ef408e8d1514a461fb72dae26a8"}, "3f7db895-e1f5-4987-ac94-ea65dfa83eba": {"doc_hash": "54af0c44d100e01be0d0ccd5001a257d7c2b704aab29831816c406314c12443a", "ref_doc_id": "0ab31f12-cc22-4821-82d4-dd1c81e4a8eb"}, "b9c4464a-30dc-47f3-8bf1-92d0169cb32f": {"doc_hash": "a8fc9545129af130c8bb6b2b7d61c1f834dac4d2f60ae5c31a12183e28551d85", "ref_doc_id": "fd570d6b-40f9-44a4-a136-0de7854c266d"}, "4b9471ca-3023-4bab-a0ba-c92a401d9ad5": {"doc_hash": "d4a3082047a5c7811b743eb37197d8f17da728ac60fba8a8ea878dee2887bc7f", "ref_doc_id": "1ce6f421-bf9e-47d1-9e66-243c163c1859"}}, "docstore/ref_doc_info": {"0ab31f12-cc22-4821-82d4-dd1c81e4a8eb": {"node_ids": ["3f7db895-e1f5-4987-ac94-ea65dfa83eba"], "metadata": {"source_type": "report", "report_id": "report_0", "ingested_at": "2025-06-18T01:29:24.919508", "content_type": "ioc_collection", "extracted_at": "2025-06-18T01:29:33.726938", "document_title": "Exploring Unique Entities in the Document: A Compilation of Candidate Titles and Content", "questions_this_excerpt_can_answer": "1. What types of IOCs (Indicators of Compromise) were extracted from the document?\n2. Were any IP addresses, domains, file hashes, URLs, or email addresses identified as potential threats in the document?\n3. What specific information was gathered regarding unique entities in the document, such as candidate titles and content?"}}, "fd570d6b-40f9-44a4-a136-0de7854c266d": {"node_ids": ["b9c4464a-30dc-47f3-8bf1-92d0169cb32f"], "metadata": {"source_type": "report", "report_id": "report_1", "ingested_at": "2025-06-18T01:29:24.919609", "content_type": "ioc_collection", "extracted_at": "2025-06-18T01:29:36.686840", "document_title": "Comprehensive List of IOCs: IP Addresses, Domains, File Hashes, URLs, and Email Addresses", "questions_this_excerpt_can_answer": "1. What types of IOCs are included in the Comprehensive List of IOCs document?\n2. When was the document ingested and extracted for analysis?\n3. Are there any specific IP addresses, domains, file hashes, URLs, or email addresses listed in the document?"}}, "1ce6f421-bf9e-47d1-9e66-243c163c1859": {"node_ids": ["4b9471ca-3023-4bab-a0ba-c92a401d9ad5"], "metadata": {"source_type": "report", "report_id": "report_2", "ingested_at": "2025-06-18T01:29:24.919625", "content_type": "ioc_collection", "extracted_at": "2025-06-18T01:29:39.608383", "document_title": "Comprehensive List of IOCs: IP Addresses, Domains, File Hashes, URLs, and Email Addresses", "questions_this_excerpt_can_answer": "1. What types of IOCs are included in the Comprehensive List of IOCs document, specifically in terms of IP addresses, domains, file hashes, URLs, and email addresses?\n2. When was the document titled \"Comprehensive List of IOCs: IP Addresses, Domains, File Hashes, URLs, and Email Addresses\" ingested and extracted for analysis?\n3. Are there any specific IP addresses, domains, file hashes, URLs, or email addresses listed in the document, or are all categories currently empty?"}}}, "docstore/data": {"3f7db895-e1f5-4987-ac94-ea65dfa83eba": {"__data__": {"id_": "3f7db895-e1f5-4987-ac94-ea65dfa83eba", "embedding": null, "metadata": {"source_type": "report", "report_id": "report_0", "ingested_at": "2025-06-18T01:29:24.919508", "content_type": "ioc_collection", "extracted_at": "2025-06-18T01:29:33.726938", "document_title": "Exploring Unique Entities in the Document: A Compilation of Candidate Titles and Content", "questions_this_excerpt_can_answer": "1. What types of IOCs (Indicators of Compromise) were extracted from the document?\n2. Were any IP addresses, domains, file hashes, URLs, or email addresses identified as potential threats in the document?\n3. What specific information was gathered regarding unique entities in the document, such as candidate titles and content?"}, "excluded_embed_metadata_keys": [], "excluded_llm_metadata_keys": [], "relationships": {"1": {"node_id": "0ab31f12-cc22-4821-82d4-dd1c81e4a8eb", "node_type": "4", "metadata": {"source_type": "report", "report_id": "report_0", "ingested_at": "2025-06-18T01:29:24.919508", "content_type": "ioc_collection", "extracted_at": "2025-06-18T01:29:33.726938"}, "hash": "182ba211936ace47b514e099a17f5c4b903502afd6f13e58581eae257bb93b38", "class_name": "RelatedNodeInfo"}}, "metadata_template": "{key}: {value}", "metadata_separator": "\n", "text": "IOCs from document: {\n  \"ip_addresses\": [],\n  \"domains\": [],\n  \"file_hashes\": [],\n  \"urls\": [],\n  \"email_addresses\": []\n}", "mimetype": "text/plain", "start_char_idx": 0, "end_char_idx": 121, "metadata_seperator": "\n", "text_template": "[Excerpt from document]\n{metadata_str}\nExcerpt:\n-----\n{content}\n-----\n", "class_name": "TextNode"}, "__type__": "1"}, "b9c4464a-30dc-47f3-8bf1-92d0169cb32f": {"__data__": {"id_": "b9c4464a-30dc-47f3-8bf1-92d0169cb32f", "embedding": null, "metadata": {"source_type": "report", "report_id": "report_1", "ingested_at": "2025-06-18T01:29:24.919609", "content_type": "ioc_collection", "extracted_at": "2025-06-18T01:29:36.686840", "document_title": "Comprehensive List of IOCs: IP Addresses, Domains, File Hashes, URLs, and Email Addresses", "questions_this_excerpt_can_answer": "1. What types of IOCs are included in the Comprehensive List of IOCs document?\n2. When was the document ingested and extracted for analysis?\n3. Are there any specific IP addresses, domains, file hashes, URLs, or email addresses listed in the document?"}, "excluded_embed_metadata_keys": [], "excluded_llm_metadata_keys": [], "relationships": {"1": {"node_id": "fd570d6b-40f9-44a4-a136-0de7854c266d", "node_type": "4", "metadata": {"source_type": "report", "report_id": "report_1", "ingested_at": "2025-06-18T01:29:24.919609", "content_type": "ioc_collection", "extracted_at": "2025-06-18T01:29:36.686840"}, "hash": "6a38be00048186c78b7d95430f6ff5fb23dda8dc709de1d8cb44c4e30d43f9bb", "class_name": "RelatedNodeInfo"}}, "metadata_template": "{key}: {value}", "metadata_separator": "\n", "text": "IOCs from document: {\n  \"ip_addresses\": [],\n  \"domains\": [],\n  \"file_hashes\": [],\n  \"urls\": [],\n  \"email_addresses\": []\n}", "mimetype": "text/plain", "start_char_idx": 0, "end_char_idx": 121, "metadata_seperator": "\n", "text_template": "[Excerpt from document]\n{metadata_str}\nExcerpt:\n-----\n{content}\n-----\n", "class_name": "TextNode"}, "__type__": "1"}, "4b9471ca-3023-4bab-a0ba-c92a401d9ad5": {"__data__": {"id_": "4b9471ca-3023-4bab-a0ba-c92a401d9ad5", "embedding": null, "metadata": {"source_type": "report", "report_id": "report_2", "ingested_at": "2025-06-18T01:29:24.919625", "content_type": "ioc_collection", "extracted_at": "2025-06-18T01:29:39.608383", "document_title": "Comprehensive List of IOCs: IP Addresses, Domains, File Hashes, URLs, and Email Addresses", "questions_this_excerpt_can_answer": "1. What types of IOCs are included in the Comprehensive List of IOCs document, specifically in terms of IP addresses, domains, file hashes, URLs, and email addresses?\n2. When was the document titled \"Comprehensive List of IOCs: IP Addresses, Domains, File Hashes, URLs, and Email Addresses\" ingested and extracted for analysis?\n3. Are there any specific IP addresses, domains, file hashes, URLs, or email addresses listed in the document, or are all categories currently empty?"}, "excluded_embed_metadata_keys": [], "excluded_llm_metadata_keys": [], "relationships": {"1": {"node_id": "1ce6f421-bf9e-47d1-9e66-243c163c1859", "node_type": "4", "metadata": {"source_type": "report", "report_id": "report_2", "ingested_at": "2025-06-18T01:29:24.919625", "content_type": "ioc_collection", "extracted_at": "2025-06-18T01:29:39.608383"}, "hash": "2ec0c17c760f835e9ab6eaf5f2606a5216676ef408e8d1514a461fb72dae26a8", "class_name": "RelatedNodeInfo"}}, "metadata_template": "{key}: {value}", "metadata_separator": "\n", "text": "IOCs from document: {\n  \"ip_addresses\": [],\n  \"domains\": [],\n  \"file_hashes\": [],\n  \"urls\": [],\n  \"email_addresses\": []\n}", "mimetype": "text/plain", "start_char_idx": 0, "end_char_idx": 121, "metadata_seperator": "\n", "text_template": "[Excerpt from document]\n{metadata_str}\nExcerpt:\n-----\n{content}\n-----\n", "class_name": "TextNode"}, "__type__": "1"}}}