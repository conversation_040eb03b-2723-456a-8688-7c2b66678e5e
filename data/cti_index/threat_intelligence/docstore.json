{"docstore/metadata": {"4ce64d67-dc5a-488d-9d3f-33aacd868bfa": {"doc_hash": "fe257fba8b19d475cd94aaff8bcddaf2aaba56a39ebb82bf2bad07559c1604d0"}, "51a3b013-7344-4f89-a5f3-839b75e06bc0": {"doc_hash": "4567c56b67c9e75e4feb10c1a8a5e772263bf2197084de3940d2dca6d0873bda"}, "40ccc460-59c8-4baf-a5ba-2ca8abde399a": {"doc_hash": "085ed766dd911437731f8678d8492c2fa925e84964a2a7b6445d741e77992beb"}, "71f778e5-1c51-4070-a7db-d860e954c9e4": {"doc_hash": "3fe0490b9b71b31d8d88d2794650d0ba544c6d1b5252419a1e95dab51f9ff287", "ref_doc_id": "4ce64d67-dc5a-488d-9d3f-33aacd868bfa"}, "7d6820d4-e5de-4c4f-b4f2-d5e32cd86a17": {"doc_hash": "5d3edada2bfdfc220097c71673aaf51a5e1d8adb8aca3623019fd909b341100b", "ref_doc_id": "51a3b013-7344-4f89-a5f3-839b75e06bc0"}, "bb626b26-de04-4780-8f74-1ddef7111aac": {"doc_hash": "3b6e6a6a45c2fbd4146f8761b0b43ce9c071ec26a3a44d88ff39ac4ac74e64b3", "ref_doc_id": "40ccc460-59c8-4baf-a5ba-2ca8abde399a"}}, "docstore/ref_doc_info": {"4ce64d67-dc5a-488d-9d3f-33aacd868bfa": {"node_ids": ["71f778e5-1c51-4070-a7db-d860e954c9e4"], "metadata": {"source_type": "report", "report_id": "report_0", "ingested_at": "2025-06-18T01:29:24.919508", "content_type": "threat_intelligence", "document_title": "\"APT29 Targets Government Agencies with New Malware Variants: A Threat Analysis\"", "questions_this_excerpt_can_answer": "1. What specific types of government agencies have been targeted by APT29 with the new malware variants?\n2. What are the characteristics of the new malware variants being used by APT29 to target government agencies?\n3. How has the threat landscape evolved with the emergence of these new malware variants used by APT29 against government agencies?"}}, "51a3b013-7344-4f89-a5f3-839b75e06bc0": {"node_ids": ["7d6820d4-e5de-4c4f-b4f2-d5e32cd86a17"], "metadata": {"source_type": "report", "report_id": "report_1", "ingested_at": "2025-06-18T01:29:24.919609", "content_type": "threat_intelligence", "document_title": "\"Lazarus Group's Advanced Attacks on Cryptocurrency Exchanges: A Comprehensive Analysis\"", "questions_this_excerpt_can_answer": "1. What specific tactics and techniques does the Lazarus Group use in their advanced attacks on cryptocurrency exchanges?\n2. How have cryptocurrency exchanges been impacted by the ongoing targeting from the Lazarus Group?\n3. What measures have cryptocurrency exchanges implemented to defend against the sophisticated attacks carried out by the Lazarus Group?"}}, "40ccc460-59c8-4baf-a5ba-2ca8abde399a": {"node_ids": ["bb626b26-de04-4780-8f74-1ddef7111aac"], "metadata": {"source_type": "report", "report_id": "report_2", "ingested_at": "2025-06-18T01:29:24.919625", "content_type": "threat_intelligence", "document_title": "\"Evolution of Ransomware Campaigns: The Rise of Double Extortion Tactics\"", "questions_this_excerpt_can_answer": "1. How have ransomware campaigns evolved in recent times, specifically in terms of tactics such as double extortion?\n2. What is the significance of the rise of double extortion tactics in ransomware campaigns, as highlighted in the document titled \"Evolution of Ransomware Campaigns: The Rise of Double Extortion Tactics\"?\n3. What insights can be gained from the analysis of recent ransomware campaigns that have increasingly utilized double extortion tactics, as discussed in the report with report_id: report_2?"}}}, "docstore/data": {"71f778e5-1c51-4070-a7db-d860e954c9e4": {"__data__": {"id_": "71f778e5-1c51-4070-a7db-d860e954c9e4", "embedding": null, "metadata": {"source_type": "report", "report_id": "report_0", "ingested_at": "2025-06-18T01:29:24.919508", "content_type": "threat_intelligence", "document_title": "\"APT29 Targets Government Agencies with New Malware Variants: A Threat Analysis\"", "questions_this_excerpt_can_answer": "1. What specific types of government agencies have been targeted by APT29 with the new malware variants?\n2. What are the characteristics of the new malware variants being used by APT29 to target government agencies?\n3. How has the threat landscape evolved with the emergence of these new malware variants used by APT29 against government agencies?"}, "excluded_embed_metadata_keys": [], "excluded_llm_metadata_keys": [], "relationships": {"1": {"node_id": "4ce64d67-dc5a-488d-9d3f-33aacd868bfa", "node_type": "4", "metadata": {"source_type": "report", "report_id": "report_0", "ingested_at": "2025-06-18T01:29:24.919508", "content_type": "threat_intelligence"}, "hash": "fe257fba8b19d475cd94aaff8bcddaf2aaba56a39ebb82bf2bad07559c1604d0", "class_name": "RelatedNodeInfo"}}, "metadata_template": "{key}: {value}", "metadata_separator": "\n", "text": "APT29 has been observed using new malware variants targeting government agencies...", "mimetype": "text/plain", "start_char_idx": 0, "end_char_idx": 83, "metadata_seperator": "\n", "text_template": "[Excerpt from document]\n{metadata_str}\nExcerpt:\n-----\n{content}\n-----\n", "class_name": "TextNode"}, "__type__": "1"}, "7d6820d4-e5de-4c4f-b4f2-d5e32cd86a17": {"__data__": {"id_": "7d6820d4-e5de-4c4f-b4f2-d5e32cd86a17", "embedding": null, "metadata": {"source_type": "report", "report_id": "report_1", "ingested_at": "2025-06-18T01:29:24.919609", "content_type": "threat_intelligence", "document_title": "\"Lazarus Group's Advanced Attacks on Cryptocurrency Exchanges: A Comprehensive Analysis\"", "questions_this_excerpt_can_answer": "1. What specific tactics and techniques does the Lazarus Group use in their advanced attacks on cryptocurrency exchanges?\n2. How have cryptocurrency exchanges been impacted by the ongoing targeting from the Lazarus Group?\n3. What measures have cryptocurrency exchanges implemented to defend against the sophisticated attacks carried out by the Lazarus Group?"}, "excluded_embed_metadata_keys": [], "excluded_llm_metadata_keys": [], "relationships": {"1": {"node_id": "51a3b013-7344-4f89-a5f3-839b75e06bc0", "node_type": "4", "metadata": {"source_type": "report", "report_id": "report_1", "ingested_at": "2025-06-18T01:29:24.919609", "content_type": "threat_intelligence"}, "hash": "4567c56b67c9e75e4feb10c1a8a5e772263bf2197084de3940d2dca6d0873bda", "class_name": "RelatedNodeInfo"}}, "metadata_template": "{key}: {value}", "metadata_separator": "\n", "text": "Lazarus group continues to target cryptocurrency exchanges with sophisticated attacks...", "mimetype": "text/plain", "start_char_idx": 0, "end_char_idx": 88, "metadata_seperator": "\n", "text_template": "[Excerpt from document]\n{metadata_str}\nExcerpt:\n-----\n{content}\n-----\n", "class_name": "TextNode"}, "__type__": "1"}, "bb626b26-de04-4780-8f74-1ddef7111aac": {"__data__": {"id_": "bb626b26-de04-4780-8f74-1ddef7111aac", "embedding": null, "metadata": {"source_type": "report", "report_id": "report_2", "ingested_at": "2025-06-18T01:29:24.919625", "content_type": "threat_intelligence", "document_title": "\"Evolution of Ransomware Campaigns: The Rise of Double Extortion Tactics\"", "questions_this_excerpt_can_answer": "1. How have ransomware campaigns evolved in recent times, specifically in terms of tactics such as double extortion?\n2. What is the significance of the rise of double extortion tactics in ransomware campaigns, as highlighted in the document titled \"Evolution of Ransomware Campaigns: The Rise of Double Extortion Tactics\"?\n3. What insights can be gained from the analysis of recent ransomware campaigns that have increasingly utilized double extortion tactics, as discussed in the report with report_id: report_2?"}, "excluded_embed_metadata_keys": [], "excluded_llm_metadata_keys": [], "relationships": {"1": {"node_id": "40ccc460-59c8-4baf-a5ba-2ca8abde399a", "node_type": "4", "metadata": {"source_type": "report", "report_id": "report_2", "ingested_at": "2025-06-18T01:29:24.919625", "content_type": "threat_intelligence"}, "hash": "085ed766dd911437731f8678d8492c2fa925e84964a2a7b6445d741e77992beb", "class_name": "RelatedNodeInfo"}}, "metadata_template": "{key}: {value}", "metadata_separator": "\n", "text": "Recent ransomware campaigns show increased use of double extortion tactics...", "mimetype": "text/plain", "start_char_idx": 0, "end_char_idx": 77, "metadata_seperator": "\n", "text_template": "[Excerpt from document]\n{metadata_str}\nExcerpt:\n-----\n{content}\n-----\n", "class_name": "TextNode"}, "__type__": "1"}}}