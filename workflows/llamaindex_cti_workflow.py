"""
LlamaIndex-powered workflow for cyber threat intelligence analysis.
"""

import logging
import json
from typing import Dict, List, Any, Optional
from datetime import datetime, timedelta

from llama_index.core import VectorStoreIndex, Document
from llama_index.core.query_engine import RetrieverQueryEngine
from llama_index.core.retrievers import VectorIndexRetriever
from llama_index.llms.openai import OpenAI

from agents.cti_agent import CTIAgent
from rag.index_builder import IndexBuilder
from rag.retriever import DocumentRetriever


class CTIWorkflow:
    """
    LlamaIndex-powered workflow for comprehensive cyber threat intelligence analysis.
    
    Combines document indexing, semantic search, and specialized CTI analysis
    to provide comprehensive threat intelligence reports.
    """
    
    def __init__(
        self,
        storage_dir: str = "./data/cti_index",
        llm_model: str = "gpt-4",
        verbose: bool = True
    ):
        """
        Initialize the CTI workflow.
        
        Args:
            storage_dir: Directory for storing CTI indexes
            llm_model: LLM model to use
            verbose: Enable verbose logging
        """
        self.storage_dir = storage_dir
        self.llm = OpenAI(model=llm_model, temperature=0.1)
        self.verbose = verbose
        self.logger = logging.getLogger(__name__)
        
        # Initialize components
        self.index_builder = IndexBuilder(storage_dir=storage_dir)
        self.cti_agent = CTIAgent()
        
        # Initialize indexes
        self.threat_index = None
        self.ioc_index = None
        self.retriever = None
        
        # Load existing indexes if available
        self._load_existing_indexes()
    
    def _load_existing_indexes(self):
        """Load existing threat intelligence indexes."""
        try:
            # Try to load main threat index
            self.threat_index = self.index_builder.load_index("threat_intelligence")
            if self.threat_index:
                self.retriever = DocumentRetriever(self.threat_index)
                self.logger.info("Loaded existing threat intelligence index")
            
            # Try to load IOC index
            self.ioc_index = self.index_builder.load_index("ioc_database")
            if self.ioc_index:
                self.logger.info("Loaded existing IOC database index")
                
        except Exception as e:
            self.logger.warning(f"Could not load existing indexes: {str(e)}")
    
    def ingest_threat_reports(
        self,
        reports: List[str] = None,
        urls: List[str] = None,
        file_paths: List[str] = None
    ) -> Dict[str, Any]:
        """
        Ingest threat intelligence reports into the knowledge base.
        
        Args:
            reports: List of report texts
            urls: List of URLs to crawl for threat reports
            file_paths: List of file paths containing threat reports
            
        Returns:
            Ingestion results and statistics
        """
        self.logger.info("Starting threat report ingestion")
        
        try:
            # Prepare documents with CTI-specific metadata
            documents = []
            
            if reports:
                for i, report in enumerate(reports):
                    metadata = {
                        "source_type": "report",
                        "report_id": f"report_{i}",
                        "ingested_at": datetime.now().isoformat(),
                        "content_type": "threat_intelligence"
                    }
                    documents.append(Document(text=report, metadata=metadata))
            
            # Build or update the threat index
            if self.threat_index is None:
                self.threat_index = self.index_builder.build_index(
                    documents=documents,
                    urls=urls,
                    file_paths=file_paths
                )
                # Save the new index
                self.index_builder.save_index(self.threat_index, "threat_intelligence")
            else:
                # Update existing index
                if documents:
                    self.threat_index = self.index_builder.update_index(
                        self.threat_index, documents
                    )
                    self.index_builder.save_index(self.threat_index, "threat_intelligence")
            
            # Initialize retriever
            self.retriever = DocumentRetriever(self.threat_index)
            
            # Extract and index IOCs from the reports
            ioc_results = self._extract_and_index_iocs(documents)
            
            result = {
                "status": "success",
                "documents_ingested": len(documents),
                "urls_processed": len(urls) if urls else 0,
                "files_processed": len(file_paths) if file_paths else 0,
                "iocs_extracted": ioc_results,
                "timestamp": datetime.now().isoformat()
            }
            
            return result
            
        except Exception as e:
            self.logger.error(f"Error ingesting threat reports: {str(e)}")
            return {
                "status": "error",
                "error": str(e),
                "timestamp": datetime.now().isoformat()
            }
    
    def _extract_and_index_iocs(self, documents: List[Document]) -> Dict[str, Any]:
        """Extract IOCs from documents and create IOC index."""
        all_iocs = {
            "ip_addresses": [],
            "domains": [],
            "file_hashes": [],
            "urls": [],
            "email_addresses": []
        }
        
        ioc_documents = []
        
        for doc in documents:
            # Extract IOCs using the CTI agent
            iocs = self.cti_agent.extract_iocs(doc.text)
            
            # Merge IOCs
            for ioc_type, ioc_list in iocs.items():
                all_iocs[ioc_type].extend(ioc_list)
            
            # Create IOC document for indexing
            ioc_text = f"IOCs from document: {json.dumps(iocs, indent=2)}"
            ioc_metadata = {
                **doc.metadata,
                "content_type": "ioc_collection",
                "extracted_at": datetime.now().isoformat()
            }
            ioc_documents.append(Document(text=ioc_text, metadata=ioc_metadata))
        
        # Build or update IOC index
        if self.ioc_index is None and ioc_documents:
            self.ioc_index = self.index_builder.build_index(documents=ioc_documents)
            self.index_builder.save_index(self.ioc_index, "ioc_database")
        elif ioc_documents:
            self.ioc_index = self.index_builder.update_index(self.ioc_index, ioc_documents)
            self.index_builder.save_index(self.ioc_index, "ioc_database")
        
        # Deduplicate IOCs
        for ioc_type in all_iocs:
            all_iocs[ioc_type] = list(set(all_iocs[ioc_type]))
        
        return {
            "total_iocs": sum(len(ioc_list) for ioc_list in all_iocs.values()),
            "by_type": {k: len(v) for k, v in all_iocs.items()},
            "iocs": all_iocs
        }
    
    def analyze_threat(
        self,
        threat_query: str,
        include_historical: bool = True,
        similarity_threshold: float = 0.7
    ) -> Dict[str, Any]:
        """
        Perform comprehensive threat analysis using indexed knowledge.
        
        Args:
            threat_query: Threat to analyze
            include_historical: Whether to include historical context
            similarity_threshold: Minimum similarity for relevant documents
            
        Returns:
            Comprehensive threat analysis
        """
        self.logger.info(f"Analyzing threat: {threat_query}")
        
        if not self.threat_index or not self.retriever:
            return {
                "error": "No threat intelligence index available. Please ingest reports first.",
                "query": threat_query
            }
        
        try:
            # Step 1: Semantic search for relevant threat intelligence
            relevant_docs = self.retriever.semantic_search(
                query=threat_query,
                top_k=10,
                similarity_threshold=similarity_threshold
            )
            
            # Step 2: Search for related IOCs
            related_iocs = self._search_related_iocs(threat_query)
            
            # Step 3: Use CTI agent for specialized analysis
            cti_analysis = self.cti_agent.analyze(
                query=threat_query,
                threat_type="general"
            )
            
            # Step 4: Historical context if requested
            historical_context = ""
            if include_historical:
                historical_context = self._get_historical_context(threat_query)
            
            # Step 5: Synthesize comprehensive analysis
            synthesis = self._synthesize_threat_analysis(
                threat_query,
                relevant_docs,
                related_iocs,
                cti_analysis,
                historical_context
            )
            
            result = {
                "query": threat_query,
                "relevant_documents": relevant_docs,
                "related_iocs": related_iocs,
                "cti_analysis": cti_analysis,
                "historical_context": historical_context,
                "synthesis": synthesis,
                "confidence_score": self._calculate_analysis_confidence(relevant_docs),
                "timestamp": datetime.now().isoformat(),
                "workflow": "CTIWorkflow"
            }
            
            return result
            
        except Exception as e:
            self.logger.error(f"Error analyzing threat: {str(e)}")
            return {
                "error": str(e),
                "query": threat_query,
                "timestamp": datetime.now().isoformat()
            }
    
    def _search_related_iocs(self, threat_query: str) -> Dict[str, Any]:
        """Search for IOCs related to the threat query."""
        if not self.ioc_index:
            return {"error": "No IOC index available"}
        
        try:
            # Create IOC retriever
            ioc_retriever = DocumentRetriever(self.ioc_index)
            
            # Search for related IOCs
            ioc_results = ioc_retriever.semantic_search(
                query=threat_query,
                top_k=5,
                similarity_threshold=0.6
            )
            
            # Extract IOCs from results
            extracted_iocs = {
                "ip_addresses": [],
                "domains": [],
                "file_hashes": [],
                "urls": [],
                "email_addresses": []
            }
            
            for result in ioc_results:
                content = result["content"]
                iocs = self.cti_agent.extract_iocs(content)
                
                for ioc_type, ioc_list in iocs.items():
                    extracted_iocs[ioc_type].extend(ioc_list)
            
            # Deduplicate
            for ioc_type in extracted_iocs:
                extracted_iocs[ioc_type] = list(set(extracted_iocs[ioc_type]))
            
            return {
                "search_results": ioc_results,
                "extracted_iocs": extracted_iocs,
                "total_iocs": sum(len(ioc_list) for ioc_list in extracted_iocs.values())
            }
            
        except Exception as e:
            self.logger.error(f"Error searching IOCs: {str(e)}")
            return {"error": str(e)}
    
    def _get_historical_context(self, threat_query: str) -> str:
        """Get historical context for the threat."""
        try:
            # Search for historical information
            historical_results = self.retriever.time_filtered_search(
                query=threat_query,
                start_date=datetime.now() - timedelta(days=365),  # Last year
                top_k=5
            )
            
            if not historical_results:
                return "No historical context available."
            
            # Summarize historical context
            context_texts = [result["content"][:500] for result in historical_results]
            combined_context = "\n".join(context_texts)
            
            summary_prompt = f"""
            Based on the historical threat intelligence below, provide a brief summary
            of the historical context for: {threat_query}
            
            Historical Information:
            {combined_context}
            
            Summary:
            """
            
            summary = self.llm.complete(summary_prompt)
            return str(summary)
            
        except Exception as e:
            self.logger.error(f"Error getting historical context: {str(e)}")
            return "Historical context not available due to error."
    
    def _synthesize_threat_analysis(
        self,
        threat_query: str,
        relevant_docs: List[Dict[str, Any]],
        related_iocs: Dict[str, Any],
        cti_analysis: Dict[str, Any],
        historical_context: str
    ) -> str:
        """Synthesize comprehensive threat analysis."""
        
        synthesis_prompt = f"""
        As a senior cyber threat intelligence analyst, synthesize the following information
        into a comprehensive threat analysis for: {threat_query}
        
        Relevant Threat Intelligence Documents:
        {json.dumps(relevant_docs[:3], indent=2)}  # Limit for prompt size
        
        Related IOCs:
        {json.dumps(related_iocs, indent=2)}
        
        CTI Agent Analysis:
        {json.dumps(cti_analysis, indent=2)}
        
        Historical Context:
        {historical_context}
        
        Please provide a comprehensive analysis including:
        1. Threat Overview and Classification
        2. Attribution Assessment
        3. Tactics, Techniques, and Procedures (TTPs)
        4. Indicators of Compromise Summary
        5. Impact Assessment
        6. Mitigation Recommendations
        7. Confidence Level and Gaps
        
        Analysis:
        """
        
        synthesis = self.llm.complete(synthesis_prompt)
        return str(synthesis)
    
    def _calculate_analysis_confidence(self, relevant_docs: List[Dict[str, Any]]) -> float:
        """Calculate confidence score for the analysis."""
        if not relevant_docs:
            return 0.0
        
        # Calculate based on number of sources and average similarity
        num_sources = len(relevant_docs)
        avg_similarity = sum(doc["score"] for doc in relevant_docs) / num_sources
        
        # Weighted confidence score
        confidence = (min(num_sources / 10, 1.0) * 0.4) + (avg_similarity * 0.6)
        return round(confidence, 2)
    
    def track_threat_campaign(
        self,
        campaign_name: str,
        keywords: List[str] = None
    ) -> Dict[str, Any]:
        """
        Set up tracking for a specific threat campaign.
        
        Args:
            campaign_name: Name of the campaign to track
            keywords: Additional keywords to monitor
            
        Returns:
            Campaign tracking configuration
        """
        self.logger.info(f"Setting up campaign tracking: {campaign_name}")
        
        # Use CTI agent's threat actor tracking
        tracking_config = self.cti_agent.track_threat_actor(campaign_name)
        
        # Add workflow-specific configuration
        tracking_config.update({
            "workflow": "CTIWorkflow",
            "keywords": keywords or [],
            "index_available": self.threat_index is not None,
            "ioc_index_available": self.ioc_index is not None
        })
        
        return tracking_config
    
    def generate_ioc_feed(
        self,
        time_range: timedelta = timedelta(days=7),
        confidence_threshold: float = 0.7
    ) -> Dict[str, Any]:
        """
        Generate an IOC feed from recent threat intelligence.
        
        Args:
            time_range: Time range for IOC collection
            confidence_threshold: Minimum confidence for IOCs
            
        Returns:
            IOC feed with metadata
        """
        self.logger.info("Generating IOC feed")
        
        if not self.ioc_index:
            return {"error": "No IOC index available"}
        
        try:
            # Search for recent IOCs
            end_date = datetime.now()
            start_date = end_date - time_range
            
            ioc_retriever = DocumentRetriever(self.ioc_index)
            recent_iocs = ioc_retriever.time_filtered_search(
                query="indicators of compromise",
                start_date=start_date,
                end_date=end_date,
                top_k=50
            )
            
            # Extract and consolidate IOCs
            consolidated_iocs = {
                "ip_addresses": [],
                "domains": [],
                "file_hashes": [],
                "urls": [],
                "email_addresses": []
            }
            
            for result in recent_iocs:
                if result["score"] >= confidence_threshold:
                    iocs = self.cti_agent.extract_iocs(result["content"])
                    for ioc_type, ioc_list in iocs.items():
                        consolidated_iocs[ioc_type].extend(ioc_list)
            
            # Deduplicate and format
            for ioc_type in consolidated_iocs:
                consolidated_iocs[ioc_type] = list(set(consolidated_iocs[ioc_type]))
            
            # Generate IOC report
            ioc_report = self.cti_agent.generate_ioc_report(
                iocs=consolidated_iocs,
                context=f"IOCs collected from {time_range.days} days of threat intelligence"
            )
            
            return {
                "feed_generated_at": datetime.now().isoformat(),
                "time_range_days": time_range.days,
                "confidence_threshold": confidence_threshold,
                "sources_analyzed": len(recent_iocs),
                "ioc_report": ioc_report,
                "workflow": "CTIWorkflow"
            }
            
        except Exception as e:
            self.logger.error(f"Error generating IOC feed: {str(e)}")
            return {"error": str(e)}
